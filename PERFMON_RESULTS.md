
Jul 24 12:17:22 :: Pulse usage new high water mark [3.57%, 3574 usec]. Trace info: 
Pulse profiling info


Section name        | Enter Count|  Exit Count|  usec total|     pulse %|max pulse % (1 entry)

------------------------------------------------------------------------------- 

Main Loop           |           1|           1|        3387|       3.39%|               3.39%

Process Commands    |           1|           1|           0|       0.00%|               0.00%

Process Input       |           1|           1|           0|       0.00%|               0.00%

Process Output      |           1|           1|           0|       0.00%|               0.00%

do_gen_door         |           1|           1|           3|       0.00%|               0.00%

do_wear             |           1|           1|          16|       0.02%|               0.02%

event_process       |           1|           1|        3245|       3.25%|               3.25%

heartbeat           |           1|           1|        3252|       3.25%|               3.25%

shop_keeper         |           1|           1|           8|       0.01%|               0.01%

Jul 24 12:17:25 :: Pulse usage new high water mark [4.76%, 4760 usec]. Trace info: 
Pulse profiling info


Section name        | Enter Count|  Exit Count|  usec total|     pulse %|max pulse % (1 entry)

------------------------------------------------------------------------------- 

Main Loop           |           1|           1|        4583|       4.58%|               4.58%

Process Commands    |           1|           1|        4510|       4.51%|               4.51%

Process Input       |           1|           1|          18|       0.02%|               0.02%

Process Output      |           1|           1|          45|       0.04%|               0.04%

event_process       |           1|           1|           1|       0.00%|               0.00%

heartbeat           |           1|           1|           2|       0.00%|               0.00%

Jul 24 12:17:27 :: Pulse usage new high water mark [41.12%, 41116 usec]. Trace info: 
Pulse profiling info


Section name        | Enter Count|  Exit Count|  usec total|     pulse %|max pulse % (1 entry)

------------------------------------------------------------------------------- 

Main Loop           |           1|           1|       40980|      40.98%|              40.98%

Process Commands    |           1|           1|       40891|      40.89%|              40.89%

Process Input       |           1|           1|          26|       0.03%|               0.03%

Process Output      |           1|           1|          53|       0.05%|               0.05%

event_process       |           1|           1|           1|       0.00%|               0.00%

heartbeat           |           1|           1|           1|       0.00%|               0.00%

Jul 24 12:17:28 :: SYSERR: 	YBrother Spire	n (#200103): Attempting to call non-existing mob function.
Jul 24 12:17:28 :: SYSERR: Jakur the tanner (#125913): Attempting to call non-existing mob function.
Jul 24 12:17:28 :: SYSERR: Adoril (#21605): Attempting to call non-existing mob function.
Jul 24 12:17:28 :: Pulse usage new high water mark [173.35%, 173353 usec]. Trace info: 
Pulse profiling info


Section name        | Enter Count|  Exit Count|  usec total|     pulse %|max pulse % (1 entry)

------------------------------------------------------------------------------- 

CastleGuard         |          13|          13|           4|       0.00%|               0.00%

DicknDavid          |           1|           1|           1|       0.00%|               0.00%

James               |           1|           1|           0|       0.00%|               0.00%

Main Loop           |           1|           1|      173206|     173.21%|             173.21%

Process Commands    |           1|           1|           0|       0.00%|               0.00%

Process Input       |           1|           1|           1|       0.00%|               0.00%

Process Output      |           1|           1|           0|       0.00%|               0.00%

abyssal_vortex      |           4|           4|           4|       0.00%|               0.00%

affect_update       |           1|           1|       30640|      30.64%|              30.64%

agrachdyrr          |           1|           1|           2|       0.00%|               0.00%

alandor_ferry       |           1|           1|           3|       0.00%|               0.00%

bloodaxe            |          10|          10|           5|       0.01%|               0.00%

bolthammer          |           4|           4|           0|       0.00%|               0.00%

celestial_leviathan |           1|           1|           0|       0.00%|               0.00%

celestial_sword     |           4|           4|           1|       0.00%|               0.00%

cf_trainingmaster   |           4|           4|           0|       0.00%|               0.00%

chan                |           5|           5|           0|       0.00%|               0.00%

chionthar_ferry     |           2|           2|           4|       0.00%|               0.00%

cleaning            |           5|           5|           0|       0.00%|               0.00%

cryogenicist        |           1|           1|           0|       0.00%|               0.00%

do_follow           |           2|           2|         840|       0.84%|               0.43%

do_gen_cast         |         374|         374|      111191|     111.19%|               1.80%

do_recline          |          10|          10|           3|       0.00%|               0.00%

do_rest             |           5|           5|           2|       0.00%|               0.00%

do_stand            |          28|          28|         103|       0.10%|               0.01%

dog                 |          24|          24|           1|       0.00%|               0.00%

dracolich_mob       |           6|           6|           1|       0.00%|               0.00%

event_process       |           1|           1|          10|       0.01%|               0.01%

flamekissed_instrument|           6|           6|           1|       0.00%|               0.00%

flamingwhip         |           1|           1|           2|       0.00%|               0.00%

frostbite           |          14|          14|           1|       0.00%|               0.00%

gatehouse_guard     |           6|           6|           3|       0.00%|               0.00%

gromph              |           1|           1|           3|       0.00%|               0.00%

guild               |          16|          16|          44|       0.04%|               0.01%

heartbeat           |           1|           1|      173184|     173.18%|             173.18%

imix                |           1|           1|           0|       0.00%|               0.00%

jerry               |           1|           1|           0|       0.00%|               0.00%

king_welmar         |           1|           1|           0|       0.00%|               0.00%

lich_mob            |          14|          14|           5|       0.01%|               0.00%

malevolence         |          10|          10|           2|       0.00%|               0.00%

mayor               |           1|           1|           0|       0.00%|               0.00%

mistweave           |          18|          18|           1|       0.00%|               0.00%

mobile_activity     |           1|           1|      133415|     133.41%|             133.41%

monk_glove          |           4|           4|           0|       0.00%|               0.00%

monk_glove_cold     |           8|           8|           4|       0.00%|               0.00%

msdp_update         |           1|           1|          12|       0.01%|               0.01%

naga                |           3|           3|           1|       0.00%|               0.00%

olhydra             |           4|           4|           3|       0.00%|               0.00%

peter               |           1|           1|           0|       0.00%|               0.00%

planetar            |           1|           1|           2|       0.00%|               0.00%

planewalker         |           1|           1|           0|       0.00%|               0.00%

player_owned_shops  |           2|           2|           3|       0.00%|               0.00%

postmaster          |           1|           1|           0|       0.00%|               0.00%

proc_update         |           1|           1|        2076|       2.08%|               2.08%

questmaster         |           4|           4|           4|       0.00%|               0.00%

receptionist        |           2|           2|           1|       0.00%|               0.00%

rune_scimitar       |          12|          12|           0|       0.00%|               0.00%

shop_keeper         |           5|           5|          13|       0.01%|               0.00%

stability_boots     |           4|           4|           3|       0.00%|               0.00%

star_circlet        |           4|           4|           0|       0.00%|               0.00%

the_prisoner        |           1|           1|           4|       0.00%|               0.00%

thrym               |           1|           1|           0|       0.00%|               0.00%

tim                 |           1|           1|         408|       0.41%|               0.41%

tom                 |           1|           1|         436|       0.44%|               0.44%

training_master     |           1|           1|           4|       0.00%|               0.00%

tyrantseye          |           2|           2|           3|       0.00%|               0.00%

update_damage_and_effects_over_time|           1|           1|        4305|       4.30%|               4.30%

vampire_cloak       |           4|           4|           1|       0.00%|               0.00%

vampire_mob         |          20|          20|           2|       0.00%|               0.00%

vaprak_claws        |           4|           4|           0|       0.00%|               0.00%

warbow              |           4|           4|           0|       0.00%|               0.00%

willowisp           |           2|           2|           1|       0.00%|               0.00%

ymir                |           1|           1|           0|       0.00%|               0.00%

zone_update         |           1|           1|           1|       0.00%|               0.00%

Jul 24 12:17:29 :: Zusuk [147.235.211.56] has connected.
Jul 24 12:17:30 :: INFO: Loading saved object data from db for: Zusuk
Jul 24 12:17:30 :: INFO: Object save header found for: Zusuk
Jul 24 12:17:30 :: Zusuk un-renting and entering game.
Jul 24 12:17:30 :: Zusuk (level 34) has 1 object (max 99999).
Jul 24 12:17:30 :: Pulse usage new high water mark [214.79%, 214792 usec]. Trace info: 
Pulse profiling info


Section name        | Enter Count|  Exit Count|  usec total|     pulse %|max pulse % (1 entry)

------------------------------------------------------------------------------- 

Main Loop           |           1|           1|      214645|     214.65%|             214.65%

Process Commands    |           1|           1|      214555|     214.56%|             214.56%

Process Input       |           1|           1|          13|       0.01%|               0.01%

Process Output      |           1|           1|          51|       0.05%|               0.05%

event_process       |           1|           1|           1|       0.00%|               0.00%

heartbeat           |           1|           1|           2|       0.00%|               0.00%

Jul 24 12:17:32 :: Pulse usage new high water mark [257.08%, 257078 usec]. Trace info: 
Pulse profiling info


Section name        | Enter Count|  Exit Count|  usec total|     pulse %|max pulse % (1 entry)

------------------------------------------------------------------------------- 

Main Loop           |           1|           1|      256911|     256.91%|             256.91%

Process Commands    |           1|           1|      256830|     256.83%|             256.83%

Process Input       |           1|           1|          18|       0.02%|               0.02%

Process Output      |           1|           1|          55|       0.06%|               0.06%

do_save             |           1|           1|      256721|     256.72%|             256.72%

event_process       |           1|           1|           1|       0.00%|               0.00%

gen_board           |           1|           1|          49|       0.05%|               0.05%

heartbeat           |           1|           1|           1|       0.00%|               0.00%

Jul 24 12:17:33 :: SYSERR:  degenerate board!  (what the hell...)
Jul 24 12:17:36 :: Object (V) 40252 does not exist in database.
Jul 24 12:17:36 :: SYSERR: award_misc_magic_item created NULL object
Jul 24 12:17:37 :: (GC) Zusuk reset entire world.
Jul 24 12:17:37 :: Pulse usage new high water mark [1022.25%, 1022250 usec]. Trace info: 
Pulse profiling info


Section name        | Enter Count|  Exit Count|  usec total|     pulse %|max pulse % (1 entry)

------------------------------------------------------------------------------- 

Main Loop           |           1|           1|     1022067|    1022.07%|            1022.07%

Process Commands    |           1|           1|     1021973|    1021.97%|            1021.97%

Process Input       |           1|           1|          20|       0.02%|               0.02%

Process Output      |           1|           1|          54|       0.05%|               0.05%

do_mload            |          37|          37|        4790|       4.79%|               0.16%

do_wear             |          39|          39|        5859|       5.86%|               0.30%

do_wield            |           3|           3|         474|       0.47%|               0.17%

do_zreset           |           1|           1|     1021888|    1021.89%|            1021.89%

event_process       |           1|           1|           1|       0.00%|               0.00%

gen_board           |           1|           1|          33|       0.03%|               0.03%

heartbeat           |           1|           1|           1|       0.00%|               0.00%

Jul 24 12:17:39 :: Zusuk has quit the game.
Jul 24 12:17:45 :: Bwarg [147.235.211.56] has connected.
Jul 24 12:17:46 :: INFO: Loading saved object data from db for: Bwarg
Jul 24 12:17:46 :: INFO: Object save header found for: Bwarg
Jul 24 12:17:46 :: Bwarg retrieving crash-saved items and entering game.
Jul 24 12:17:47 :: Bwarg (level 30) has 796 objects (max 99999).
Jul 24 12:20:28 :: SYSERR: Mob using '((ch)->player_specials->saved.psionic_energy_type)' at magic.c:3911.
Jul 24 12:22:22 :: nusage: 1   sockets connected, 1   sockets playing
Jul 24 12:24:58 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:24:58 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:00 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:00 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:00 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:00 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:00 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:00 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:00 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:01 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:01 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:02 :: SYSERR: Mob using '((ch)->player_specials->saved.pref)' at spec_procs.c:6053.
Jul 24 12:25:02 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:04 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:04 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:04 :: SYSERR: Mob using '((ch)->player_specials->saved.psionic_energy_type)' at magic.c:3747.
Jul 24 12:25:04 :: SYSERR: Mob using '((ch)->player_specials->saved.psionic_energy_type)' at magic.c:3751.
Jul 24 12:25:06 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:06 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:06 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:06 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:06 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:06 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:06 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:06 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:06 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:06 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:06 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:08 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:10 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:10 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:12 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:12 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:12 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:12 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:16 :: SYSERR: Mob using '((ch)->player_specials->saved.pref)' at spec_procs.c:6053.
Jul 24 12:25:18 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:18 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:18 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:18 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:18 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:20 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:20 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:20 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:20 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:22 :: SYSERR: Mob using '((ch)->player_specials->saved.pref)' at spec_procs.c:6053.
Jul 24 12:25:22 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:22 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:24 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:24 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:24 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:24 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:24 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:26 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:28 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:28 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:30 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:30 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:30 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:30 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:30 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:32 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:32 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:34 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:34 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:36 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:36 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:36 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:36 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9122.
Jul 24 12:25:36 :: SYSERR: Mob using '((ch->master)->player_specials->saved.pref)' at utils.c:9143.
Jul 24 12:25:54 :: SYSERR: Mob using '((ch)->player_specials->saved.pref)' at spec_procs.c:6053.
Jul 24 12:25:56 :: SYSERR: Mob using '((ch)->player_specials->saved.pref)' at spec_procs.c:6053.
Jul 24 12:26:16 :: SYSERR: Mob using '((ch)->player_specials->saved.pref)' at spec_procs.c:6053.
Jul 24 12:26:35 :: Bwarg has quit the game.
Jul 24 12:26:38 :: Zusuk [147.235.211.56] has connected.
Jul 24 12:26:39 :: INFO: Loading saved object data from db for: Zusuk
Jul 24 12:26:39 :: INFO: Object save header found for: Zusuk
Jul 24 12:26:39 :: Zusuk un-renting and entering game.
Jul 24 12:26:39 :: Zusuk (level 34) has 1 object (max 99999).
Jul 24 12:27:22 :: nusage: 1   sockets connected, 1   sockets playing
